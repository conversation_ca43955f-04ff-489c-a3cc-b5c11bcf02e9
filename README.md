# pos

[![Netlify Status](https://api.netlify.com/api/v1/badges/a8f056a5-ea89-4e2e-8e7e-ffbbcc831323/deploy-status)](https://app.netlify.com/sites/app-wino-pos/deploys)

**`pos` is the modern and evolutive wine merchant's point of sale.**

## ⚡️ Requirements

- A solid environement with [Node.js](https://nodejs.org/en/) (install [nvm](https://github.com/nvm-sh/nvm) to manage versions)
- A terminal like [iTerm](https://www.iterm2.com/)
- A powerful IDE like [Visual Studio Code](https://code.visualstudio.com/)
- A great web browser like [Google Chrome](https://www.google.com/chrome/)
- For Mac users with Apple chip, it might be necessary to add Rosetta
  `/usr/sbin/softwareupdate --install-rosetta --agree-to-licens`

## 🔥 Stack

- [ReScript](https://rescript-lang.org/) `10.1.x`, a simple, fast & type safe code that leverages the JavaScript & OCaml ecosystems
- [React](https://reactjs.org/) `16.14.x`, a JavaScript library for building user interfaces
- [GraphQL](https://graphql.org/), a query language for your API
- [Apollo](https://www.apollographql.com/client/) `3.4.x`, a fully-featured, production ready caching GraphQL client

## 💻 How to use

Install as git project:

```shell
$ git clone https://github.com/winoteam/pos
$ cd pos
$ nvm use
$ yarn build
```

Then add a `.env` environment file from the `.env.default` file and run:

```
$ yarn dev:res
```

then in another process

```
$ yarn dev:server
```

Open http://localhost:3000 to view it in the browser.

The page will automatically reload if you make changes to the code.
You will see the build errors and lint warnings in the console.

## 🧩 Recommendations

Recommendations for ease of newcomers:

- Keep in mind that that the front-end codebase should focus on the view and contains minimum/no business logic or data handling (=focus on GraphQL/REST data consumption).
- Build straightforward primitives. [Don't abuse overly fancy features. Do leave some breathing room for future APIs but don't over-architect things.](https://rescript-lang.org/docs/manual/latest/project-structure#paradigm)
- [Keep third-party deps to a minimum](https://rescript-lang.org/docs/manual/latest/project-structure#third-party-dependencies)
- [Try not to have too many nested folders. Keep your project flat, and have fewer files (reminder: you can use nested modules).](https://rescript-lang.org/docs/manual/latest/project-structure#folders)

## 🚨 Caveats & legacy

For historical reasons, where [the cross-platform vision](https://reactnative.dev/docs/platform-specific-code) was emphasized, there are still some [React Native](https://reactnative.dev) deps. Since then, we've decided to break away from it but it takes time. This is an important caveat to note before getting started.

Also, we're not up to date on all the deps/ecosystem practices, so here's the plan for the next months/years:

- [Upgrade to React v18](https://react.dev/blog/2022/03/29/react-v18)
- Move to [`<StrictMode>`](https://react.dev/reference/react/StrictMode)
- [Upgrade to ReScript v11](https://rescript-lang.org/blog/release-11-0-0)
- [Bump to JSX v4](https://rescript-lang.org/docs/react/latest/migrate-react) to use the new idiomatic record-based representation of React component
- Move away from Apollo Client to a lightweight GraphQL client like [Urql](https://github.com/urql-graphql/urql)
- Bump to new [new `RescriptCore` stlib](https://forum.rescript-lang.org/t/ann-rescript-core-a-new-batteries-included-drop-in-standard-library/4149)
- [Better stack trace and in-browser debugging experience](https://github.com/rescript-association/rescript-core/pull/72)
- Move to ReScript [uncurried mode](https://rescript-lang.org/blog/uncurried-mode) to prepare ReScript upgrade to v12.0 (today [available in beta](https://github.com/rescript-lang/rescript/releases?q=12.0.0&expanded=true))
- [Supercharge GraphQL with Fragments](https://relay.dev/docs/tutorial/fragments-1/) (with Relay ?) to improve co-located data requirements and modularity
- Move the repo to the monorepo and improve CI/CD setup

Some prs are in progress to track this work. 👀

---

There are still bottlenecks in the dev setup:

- waiting for a Vitest transformer with ReScript source map support
- it's always difficult to maintain bindings and most of the bugs come from an error in the interoper translation layers
- how to get React Fast Refresh to work (really well) ?
- building a frontend monolithic app becomes increasingly difficult to maintain at some point
- do we need e2e testing ?

## 🕺 Contribute

**Want to hack on `pos`? Follow the next instructions: :rocket:**

1. Fork this repository to your own GitHub account and then clone it to your local device
2. Install dependencies using Yarn: `yarn`
3. Ensure that the build task is passing using `yarn build`
4. Send a pull request 🙌

Remember to add tests for your change.
